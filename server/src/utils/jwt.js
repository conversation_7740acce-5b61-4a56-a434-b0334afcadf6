import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret_key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

/**
 * 生成JWT token
 * @param {Object} payload - 要编码的数据
 * @param {string} expiresIn - 过期时间
 * @returns {string} JWT token
 */
export function generateToken(payload, expiresIn = JWT_EXPIRES_IN) {
  try {
    return jwt.sign(payload, JWT_SECRET, { expiresIn });
  } catch (error) {
    console.error('JWT生成失败:', error);
    throw new Error('Token生成失败');
  }
}

/**
 * 验证JWT token
 * @param {string} token - JWT token
 * @returns {Object} 解码后的数据
 */
export function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token已过期');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('无效的Token');
    } else {
      throw new Error('Token验证失败');
    }
  }
}

/**
 * 从请求头中提取token
 * @param {Object} req - Express请求对象
 * @returns {string|null} token或null
 */
export function extractTokenFromHeader(req) {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  return null;
}

/**
 * JWT认证中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - 下一个中间件函数
 */
export function authenticateToken(req, res, next) {
  const token = extractTokenFromHeader(req);
  
  if (!token) {
    return res.status(401).json({
      error: '访问被拒绝，需要提供认证token'
    });
  }

  try {
    const decoded = verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({
      error: error.message
    });
  }
}

/**
 * 管理员认证中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - 下一个中间件函数
 */
export function authenticateAdmin(req, res, next) {
  const token = extractTokenFromHeader(req);

  console.log('管理员认证中间件 - Token:', token ? '存在' : '不存在');

  if (!token) {
    console.log('管理员认证失败: 没有提供token');
    return res.status(401).json({
      error: '访问被拒绝，需要管理员权限'
    });
  }

  try {
    const decoded = verifyToken(token);
    console.log('Token解析结果:', {
      id: decoded.id,
      username: decoded.username,
      isAdmin: decoded.isAdmin,
      role: decoded.role
    });

    // 检查是否为管理员token
    if (!decoded.isAdmin) {
      console.log('管理员认证失败: isAdmin字段为', decoded.isAdmin);
      return res.status(403).json({
        error: '权限不足，需要管理员权限'
      });
    }

    req.admin = decoded;
    console.log('管理员认证成功');
    next();
  } catch (error) {
    console.log('Token验证失败:', error.message);
    return res.status(403).json({
      error: error.message
    });
  }
}
